import { getRoutes } from '../router';

export function setupHeader() {
  const headerElement = document.querySelector<HTMLElement>('#header');
  if (!headerElement) return;

  const routes = getRoutes();

  headerElement.innerHTML = `
    <div class="top-bar">
      <div class="top-bar-left">
        <span>Fruit Export Group</span>
        <a href="#">E-CATALOGUE</a>
      </div>
      <div class="top-bar-right">
        <div class="social-icons">
          <a href="#" aria-label="Facebook"><i class="fa-brands fa-facebook-f"></i></a>
          <a href="#" aria-label="Twitter"><i class="fa-brands fa-twitter"></i></a>
          <a href="#" aria-label="LinkedIn"><i class="fa-brands fa-linkedin-in"></i></a>
          <a href="#" aria-label="YouTube"><i class="fa-brands fa-youtube"></i></a>
        </div>
        <a href="#">Tiếng Việt</a>
        <a href="#" aria-label="Search"><i class="fa-solid fa-search"></i></a>
      </div>
    </div>
    <div class="nav-container">
      <div class="logo">
        <a href="/">
          <img src="/logo.png" alt="Fruit Export Logo">
        </a>
      </div>
      <nav class="main-nav">
        ${routes.map(route => `
          <a href="${route.path}" ${window.location.pathname === route.path ? 'class="active"' : ''}>${route.title}</a>
        `).join('')}
      </nav>
    </div>
  `;

  // Add Font Awesome for icons
  const fontAwesomeLink = document.createElement('link');
  fontAwesomeLink.rel = 'stylesheet';
  fontAwesomeLink.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css';
  document.head.appendChild(fontAwesomeLink);
}
