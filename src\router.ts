import { renderHomePage } from './pages/home';
import { renderAboutPage } from './pages/about';
import { renderProductsPage } from './pages/products';
import { renderMediaPage } from './pages/media';
import { renderContactPage } from './pages/contact';

type Route = {
  path: string;
  title: string;
  render: () => void;
};

const routes: Route[] = [
  { path: '/', title: 'TRANG CHỦ', render: renderHomePage },
  { path: '/gioi-thieu', title: 'GIỚI THIỆU', render: renderAboutPage },
  { path: '/san-pham', title: 'SẢN PHẨM', render: renderProductsPage },
  { path: '/truyen-thong', title: 'TRUYỀN THÔNG', render: renderMediaPage },
  { path: '/lien-he', title: 'LIÊN HỆ', render: renderContactPage },
];

export function setupRouter() {
  const contentElement = document.querySelector<HTMLElement>('#content');
  
  function navigateTo(path: string) {
    const route = routes.find(route => route.path === path) || routes[0];
    window.history.pushState({}, route.title, path);
    document.title = `Fruit Export - ${route.title}`;
    route.render();
    
    // Update active nav link
    document.querySelectorAll('.main-nav a').forEach(link => {
      if (link.getAttribute('href') === path) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }
  
  // Handle navigation clicks
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const navLink = target.closest('a');
    
    if (navLink && navLink.getAttribute('href')?.startsWith('/')) {
      e.preventDefault();
      navigateTo(navLink.getAttribute('href') || '/');
    }
  });
  
  // Handle browser back/forward
  window.addEventListener('popstate', () => {
    const path = window.location.pathname;
    const route = routes.find(route => route.path === path) || routes[0];
    route.render();
  });
  
  // Initial route
  const path = window.location.pathname;
  const initialRoute = routes.find(route => route.path === path) || routes[0];
  initialRoute.render();
  
  return { routes, navigateTo };
}

export function getRoutes() {
  return routes;
}
