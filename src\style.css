:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.87);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header Styles */
header {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.top-bar {
  background-color: #e31c25;
  color: white;
  padding: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar-left, .top-bar-right {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 0 20px;
}

.social-icons {
  display: flex;
  gap: 10px;
}

.social-icons a {
  color: white;
  text-decoration: none;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
}

.logo {
  height: 50px;
}

.logo img {
  height: 100%;
}

.main-nav {
  display: flex;
  gap: 20px;
}

.main-nav a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 8px 12px;
  transition: color 0.3s;
}

.main-nav a:hover, .main-nav a.active {
  color: #e31c25;
}

/* Main Content */
main {
  flex: 1;
  padding: 20px;
}

.hero {
  width: 100%;
  height: 500px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 40px;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 30px;
}

.btn {
  display: inline-block;
  background-color: #e31c25;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: #c01820;
}

.section {
  margin-bottom: 60px;
}

.section-title {
  font-size: 2rem;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
}

.section-title::after {
  content: '';
  display: block;
  width: 80px;
  height: 3px;
  background-color: #e31c25;
  margin: 15px auto 0;
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.product-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-image {
  height: 200px;
  width: 100%;
  object-fit: cover;
}

.product-info {
  padding: 20px;
}

.product-title {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

/* Footer Styles */
footer {
  background-color: #e31c25;
  color: white;
  padding: 40px 0 20px;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-logo {
  margin-bottom: 20px;
}

.footer-logo img {
  height: 60px;
}

.footer-info {
  flex: 1;
  min-width: 300px;
  margin-bottom: 30px;
}

.footer-info h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.contact-info {
  list-style: none;
}

.contact-info li {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}

.contact-info i {
  margin-right: 10px;
  min-width: 20px;
}

.footer-social {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  transition: background-color 0.3s;
}

.footer-social a:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  margin-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

@media (max-width: 768px) {
  .main-nav {
    display: none;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .footer-content {
    flex-direction: column;
  }
}
