export function renderContactPage() {
  const contentElement = document.querySelector<HTMLElement>('#content');
  if (!contentElement) return;

  contentElement.innerHTML = `
    <div class="hero" style="background-image: url('https://images.unsplash.com/photo-1577563908411-5077b6dc7624?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80')">
      <div class="hero-content">
        <h1>LIÊN HỆ</h1>
        <p>Kết nối với chúng tôi</p>
      </div>
    </div>

    <section class="section">
      <h2 class="section-title">Thông Tin Liên Hệ</h2>
      <div class="contact-container">
        <div class="contact-info-container">
          <div class="contact-info">
            <h3>Tr<PERSON> sở chính</h3>
            <ul>
              <li><i class="fas fa-map-marker-alt"></i> 47 <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Tỉnh Nghệ An</li>
              <li><i class="fas fa-phone-alt"></i> +84 28 37 447 666</li>
              <li><i class="fas fa-envelope"></i> <EMAIL></li>
              <li><i class="fas fa-globe"></i> www.fruitexport.com</li>
            </ul>
          </div>

          <div class="contact-info">
            <h3>Văn phòng TP. Hồ Chí Minh</h3>
            <ul>
              <li><i class="fas fa-map-marker-alt"></i> 123 Nguyễn Văn Linh, Quận 7, TP. Hồ Chí Minh</li>
              <li><i class="fas fa-phone-alt"></i> +84 28 3822 9999</li>
              <li><i class="fas fa-envelope"></i> <EMAIL></li>
            </ul>
          </div>
        </div>

        <div class="contact-form-container">
          <h3>Gửi Thông Tin Liên Hệ</h3>
          <form id="contactForm" class="contact-form">
            <div class="form-group">
              <label for="name">Họ và tên</label>
              <input type="text" id="name" name="name" required>
            </div>

            <div class="form-group">
              <label for="email">Email</label>
              <input type="email" id="email" name="email" required>
            </div>

            <div class="form-group">
              <label for="phone">Số điện thoại</label>
              <input type="tel" id="phone" name="phone">
            </div>

            <div class="form-group">
              <label for="subject">Chủ đề</label>
              <input type="text" id="subject" name="subject">
            </div>

            <div class="form-group">
              <label for="message">Nội dung</label>
              <textarea id="message" name="message" rows="5" required></textarea>
            </div>

            <button type="submit" class="btn">Gửi</button>
          </form>
        </div>
      </div>
    </section>

    <section class="section">
      <h2 class="section-title">Bản Đồ</h2>
      <div class="map-container">
        <!-- Map container -->
        <div class="map-placeholder">
          <div class="empty-map">Google Maps</div>
        </div>
      </div>
    </section>

    <style>
      .contact-container {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
      }

      .contact-info-container {
        flex: 1;
        min-width: 300px;
      }

      .contact-info {
        margin-bottom: 30px;
      }

      .contact-info h3 {
        margin-bottom: 15px;
        color: #e31c25;
      }

      .contact-info ul {
        list-style: none;
      }

      .contact-info li {
        margin-bottom: 10px;
        display: flex;
        align-items: flex-start;
      }

      .contact-info i {
        margin-right: 10px;
        min-width: 20px;
        color: #e31c25;
      }

      .contact-form-container {
        flex: 1;
        min-width: 300px;
      }

      .contact-form-container h3 {
        margin-bottom: 20px;
        color: #e31c25;
      }

      .contact-form {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .form-group {
        display: flex;
        flex-direction: column;
      }

      .form-group label {
        margin-bottom: 5px;
      }

      .form-group input,
      .form-group textarea {
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .map-container {
        margin-top: 20px;
      }

      .map-placeholder {
        width: 100%;
        height: 400px;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .empty-map {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f0f0;
        color: #666;
        font-size: 1.5rem;
      }
    </style>

    <script>
      document.getElementById('contactForm')?.addEventListener('submit', function(e) {
        e.preventDefault();
        alert('Cảm ơn bạn đã gửi thông tin. Chúng tôi sẽ liên hệ lại trong thời gian sớm nhất!');
        this.reset();
      });
    </script>
  `;
}
